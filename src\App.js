import React from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import Layout from './components/layout/Layout';
import Login from './features/auth/Login';
import Dashboard from './features/dashboard/Dashboard';
import PsychologyPage from './features/psychology/pages/PsychologyPage';
import ConsultationDetail from './features/psychology/pages/ConsultationDetail';
import DoctorBiography from './features/psychology/pages/DoctorBiography';
import WorkshopPage from './features/workshop/WorkshopPage';
import DailyServicesPage from './features/daily-services/DailyServicesPage';
import AdminDashboard from './features/admin/AdminDashboard';
import './styles/App.css';

function App() {
  return (
    <BrowserRouter>
      <Routes>
        {/* Redirect root to login */}
        <Route path="/" element={<Navigate to="/login" replace />} />

        {/* Auth Routes - No Layout */}
        <Route path="/login" element={<Login />} />

        {/* Protected Routes - With Layout */}
        <Route path="/dashboard" element={<Layout><Dashboard /></Layout>} />
        <Route path="/workshop" element={<Layout><WorkshopPage /></Layout>} />
        <Route path="/psychology" element={<Layout><PsychologyPage /></Layout>} />
        <Route path="/psychology/consultation" element={<Layout><ConsultationDetail /></Layout>} />
        <Route path="/psychology/doctor/:id" element={<DoctorBiography />} />
        <Route path="/services" element={<Layout><Dashboard /></Layout>} />
        <Route path="/daily-services" element={<Layout><DailyServicesPage /></Layout>} />
        <Route path="/admin" element={<Layout><AdminDashboard /></Layout>} />
      </Routes>
    </BrowserRouter>
  );
}

export default App;
