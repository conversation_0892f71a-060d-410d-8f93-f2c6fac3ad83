import React from 'react';

const ConsultationSection = () => {
  const handleBooking = () => {
    // Redirect to psychology page
    window.location.href = '/psychology';
  };

  return (
    <section id="contact" className="py-20 bg-gray-100">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center max-w-6xl mx-auto">
          {/* Left Content */}
          <div>
            <p className="text-gray-600 mb-2">Butuh dukungan?</p>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
              <PERSON><PERSON><PERSON><PERSON>
            </h2>
            <p className="text-lg text-gray-600 mb-8">
              dengan psikolog kami hari ini!
            </p>

            <button
              onClick={handleBooking}
              className="px-8 py-4 bg-black text-white font-semibold rounded-lg hover:bg-gray-800 transition-all duration-300"
            >
              Booking Sekarang
            </button>
          </div>

          {/* Right Image */}
          <div className="relative">
            <div className="rounded-2xl overflow-hidden shadow-lg">
              <img
                src="https://images.pexels.com/photos/7176026/pexels-photo-7176026.jpeg?auto=compress&cs=tinysrgb&w=800&h=500&fit=crop"
                alt="Konsultasi Psikologi"
                className="w-full h-80 object-cover"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ConsultationSection;
