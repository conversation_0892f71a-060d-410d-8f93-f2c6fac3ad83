# Create folder structure for reorganization
$folders = @(
    "src\features\dashboard",
    "src\features\psychology",
    "src\features\workshop", 
    "src\features\daily-services",
    "src\features\admin",
    "src\hooks",
    "src\services", 
    "src\utils",
    "src\constants",
    "src\assets",
    "src\styles"
)

foreach ($folder in $folders) {
    if (!(Test-Path $folder)) {
        New-Item -ItemType Directory -Path $folder -Force
        Write-Host "Created: $folder"
    } else {
        Write-Host "Already exists: $folder"
    }
}
