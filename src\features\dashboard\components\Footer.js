import React from 'react';
import { FaFacebook, FaTwitter, FaInstagram, FaLinkedin, FaPhone, FaEnvelope, FaMapMarkerAlt, FaWhatsapp } from 'react-icons/fa';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const services = [
    { name: 'Bengkel <PERSON>', path: '/workshop' },
    { name: 'Konsultasi Psikologi', path: '/psychology' },
    { name: '<PERSON><PERSON>', path: '/daily-services' },
    { name: 'Konsultasi Gratis', path: '#contact' }
  ];

  const quickLinks = [
    { name: '<PERSON><PERSON><PERSON>', path: '#about' },
    { name: '<PERSON><PERSON><PERSON>', path: '#services' },
    { name: '<PERSON><PERSON><PERSON>', path: '#contact' },
    { name: 'FAQ', path: '#faq' },
    { name: 'Syar<PERSON> & Ke<PERSON>tuan', path: '/terms' },
    { name: '<PERSON><PERSON><PERSON><PERSON>', path: '/privacy' }
  ];

  const socialLinks = [
    { icon: <FaFacebook />, url: 'https://facebook.com/futurex', name: 'Facebook' },
    { icon: <FaInstagram />, url: 'https://instagram.com/futurex', name: 'Instagram' },
    { icon: <FaTwitter />, url: 'https://twitter.com/futurex', name: 'Twitter' },
    { icon: <FaLinkedin />, url: 'https://linkedin.com/company/futurex', name: 'LinkedIn' }
  ];

  return (
    <footer className="bg-gray-900 text-white">
      {/* Main Footer Content */}
      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <h3 className="text-2xl font-bold mb-4">Future X</h3>
            <p className="text-gray-300 mb-6 leading-relaxed">
              Perusahaan teknologi otomotif terdepan yang menghadirkan inovasi untuk masa depan transportasi.
              Solusi cerdas untuk era digital.
            </p>
            
            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <FaPhone className="text-blue-400" />
                <span className="text-gray-300">+62 812-3456-7890</span>
              </div>
              <div className="flex items-center space-x-3">
                <FaEnvelope className="text-blue-400" />
                <span className="text-gray-300"><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3">
                <FaMapMarkerAlt className="text-blue-400" />
                <span className="text-gray-300">Jakarta, Indonesia</span>
              </div>
            </div>
          </div>

          {/* Services */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Layanan Kami</h4>
            <ul className="space-y-2">
              {services.map((service, index) => (
                <li key={index}>
                  <a 
                    href={service.path}
                    className="text-gray-300 hover:text-white transition-colors duration-300"
                  >
                    {service.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Tautan Cepat</h4>
            <ul className="space-y-2">
              {quickLinks.map((link, index) => (
                <li key={index}>
                  <a 
                    href={link.path}
                    className="text-gray-300 hover:text-white transition-colors duration-300"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Newsletter & Social */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Tetap Terhubung</h4>
            <p className="text-gray-300 mb-4">
              Dapatkan update terbaru dan penawaran khusus dari kami.
            </p>
            
            {/* Newsletter */}
            <div className="mb-6">
              <div className="flex">
                <input
                  type="email"
                  placeholder="Email Anda"
                  className="flex-1 px-4 py-2 bg-gray-800 border border-gray-700 rounded-l-lg focus:outline-none focus:border-blue-500"
                />
                <button className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-r-lg transition-colors duration-300">
                  Subscribe
                </button>
              </div>
            </div>

            {/* Social Links */}
            <div>
              <h5 className="text-sm font-medium mb-3">Ikuti Kami</h5>
              <div className="flex space-x-3">
                {socialLinks.map((social, index) => (
                  <a
                    key={index}
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-10 h-10 bg-gray-800 hover:bg-blue-600 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110"
                    aria-label={social.name}
                  >
                    {social.icon}
                  </a>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-800">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-gray-400 text-sm mb-4 md:mb-0">
              © {currentYear} Future X. All rights reserved.
            </div>
            
            {/* Quick Contact */}
            <div className="flex items-center space-x-4">
              <a
                href="tel:+6281234567890"
                className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors duration-300"
              >
                <FaPhone className="text-sm" />
                <span className="text-sm">Call Us</span>
              </a>
              <a
                href="https://wa.me/6281234567890"
                className="flex items-center space-x-2 text-gray-400 hover:text-green-400 transition-colors duration-300"
              >
                <FaWhatsapp className="text-sm" />
                <span className="text-sm">WhatsApp</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
