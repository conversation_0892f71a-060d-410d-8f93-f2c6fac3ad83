import React, { useState } from 'react';
import { FaStar, FaClock, FaCalendarAlt } from 'react-icons/fa';
import ScheduleList from './ScheduleList';

const PsychologistCard = ({ psychologist, onSelect }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Format jadwal untuk ScheduleList
  const formattedSchedules = [
    {
      id: 1,
      day: 'Senin',
      time: '09:00 - 10:00',
      isAvailable: true
    },
    {
      id: 2,
      day: 'Senin',
      time: '10:00 - 11:00',
      isAvailable: false
    },
    {
      id: 3,
      day: 'Senin',
      time: '11:00 - 12:00',
      isAvailable: true
    },
    {
      id: 4,
      day: 'Rabu',
      time: '09:00 - 10:00',
      isAvailable: true
    },
    {
      id: 5,
      day: 'Rabu',
      time: '10:00 - 11:00',
      isAvailable: true
    },
    {
      id: 6,
      day: 'Jumat',
      time: '09:00 - 10:00',
      isAvailable: false
    },
    {
      id: 7,
      day: 'Jumat',
      time: '10:00 - 11:00',
      isAvailable: true
    }
  ];

  const handleScheduleSelect = (schedule) => {
    onSelect({ ...psychologist, selectedSchedule: schedule });
  };

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl">
      {/* Header dengan foto dan info dasar */}
      <div className="relative">
        <img 
          src={psychologist.image} 
          alt={psychologist.name}
          className="w-full h-48 object-cover"
        />
        <div className="absolute top-4 right-4 bg-white px-3 py-1 rounded-full flex items-center space-x-1">
          <FaStar className="text-yellow-400" />
          <span className="font-semibold">{psychologist.rating}</span>
        </div>
      </div>

      {/* Info Psikolog */}
      <div className="p-6">
        <h3 className="text-xl font-bold text-gray-800 mb-2">{psychologist.name}</h3>
        <p className="text-gray-600 mb-4">{psychologist.specialization}</p>
        
        {/* Jadwal Tersedia */}
        <div className="flex items-center text-gray-600 mb-4">
          <FaClock className="mr-2" />
          <span>{psychologist.availableHours} jam tersedia</span>
        </div>

        {/* Biografi Singkat */}
        <p className="text-gray-600 text-sm mb-4 line-clamp-3">
          {psychologist.bio}
        </p>

        {/* Tombol Lihat Detail */}
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="text-blue-600 hover:text-blue-700 text-sm font-medium mb-4"
        >
          {isExpanded ? 'Tutup' : 'Lihat Detail'}
        </button>

        {/* Detail Expanded */}
        {isExpanded && (
          <div className="space-y-4">
            {/* Pengalaman */}
            <div>
              <h4 className="font-semibold text-gray-800 mb-2">Pengalaman</h4>
              <ul className="list-disc list-inside text-gray-600 text-sm space-y-1">
                {psychologist.experience.map((exp, index) => (
                  <li key={index}>{exp}</li>
                ))}
              </ul>
            </div>

            {/* Pendidikan */}
            <div>
              <h4 className="font-semibold text-gray-800 mb-2">Pendidikan</h4>
              <ul className="list-disc list-inside text-gray-600 text-sm space-y-1">
                {psychologist.education.map((edu, index) => (
                  <li key={index}>{edu}</li>
                ))}
              </ul>
            </div>

            {/* Jadwal Konsultasi */}
            <ScheduleList 
              schedules={formattedSchedules}
              onSelect={handleScheduleSelect}
            />
          </div>
        )}

        {/* Tombol Pilih */}
        <button
          onClick={() => onSelect(psychologist)}
          className="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200"
        >
          Pilih Jadwal
        </button>
      </div>
    </div>
  );
};

export default PsychologistCard; 