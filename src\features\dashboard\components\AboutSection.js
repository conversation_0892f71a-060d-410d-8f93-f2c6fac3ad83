import React from 'react';
import { FaCheckCircle, FaUsers, FaAward, FaClock } from 'react-icons/fa';

const AboutSection = () => {
  const stats = [
    {
      icon: <FaUsers className="text-3xl text-blue-600" />,
      number: "10,000+",
      label: "Pelanggan Puas"
    },
    {
      icon: <FaAward className="text-3xl text-green-600" />,
      number: "5+",
      label: "Tahun Pengalaman"
    },
    {
      icon: <FaCheckCircle className="text-3xl text-purple-600" />,
      number: "50,000+",
      label: "<PERSON>ana<PERSON>"
    },
    {
      icon: <FaClock className="text-3xl text-orange-600" />,
      number: "24/7",
      label: "Dukungan"
    }
  ];

  return (
    <section id="about" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
              Tentang Kami
            </h2>
            <p className="text-lg text-gray-600 mb-6 leading-relaxed">
              Future X adalah perusahaan teknologi otomotif terdepan yang menghadirkan solusi inovatif untuk
              masa depan transportasi. Kami menggabungkan teknologi canggih dengan layanan berkualitas tinggi
              untuk memberikan pengalaman terbaik bagi pelanggan.
            </p>
            <p className="text-lg text-gray-600 mb-8 leading-relaxed">
              Dengan tim ahli berpengalaman dan teknologi mutakhir, kami berkomitmen menghadirkan revolusi
              dalam industri otomotif. Visi kami adalah menciptakan ekosistem transportasi yang lebih cerdas,
              efisien, dan berkelanjutan.
            </p>

            {/* Features */}
            <div className="space-y-4 mb-8">
              <div className="flex items-center space-x-3">
                <FaCheckCircle className="text-green-500 text-xl" />
                <span className="text-gray-700">Teknologi otomotif terdepan</span>
              </div>
              <div className="flex items-center space-x-3">
                <FaCheckCircle className="text-green-500 text-xl" />
                <span className="text-gray-700">Solusi inovatif dan berkelanjutan</span>
              </div>
              <div className="flex items-center space-x-3">
                <FaCheckCircle className="text-green-500 text-xl" />
                <span className="text-gray-700">Tim ahli berpengalaman</span>
              </div>
              <div className="flex items-center space-x-3">
                <FaCheckCircle className="text-green-500 text-xl" />
                <span className="text-gray-700">Layanan premium berkualitas tinggi</span>
              </div>
            </div>

            <button className="px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105">
              Pelajari Lebih Lanjut
            </button>
          </div>

          {/* Right Image */}
          <div className="relative">
            <div className="relative rounded-2xl overflow-hidden shadow-2xl">
              <img
                src="https://images.pexels.com/photos/3184465/pexels-photo-3184465.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop"
                alt="Tim HaloBantu"
                className="w-full h-96 object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
            </div>
            
            {/* Floating Stats Card */}
            <div className="absolute -bottom-8 -left-8 bg-white rounded-xl shadow-xl p-6 border">
              <div className="grid grid-cols-2 gap-4">
                {stats.map((stat, index) => (
                  <div key={index} className="text-center">
                    <div className="flex justify-center mb-2">
                      {stat.icon}
                    </div>
                    <div className="text-2xl font-bold text-gray-800">{stat.number}</div>
                    <div className="text-sm text-gray-600">{stat.label}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
