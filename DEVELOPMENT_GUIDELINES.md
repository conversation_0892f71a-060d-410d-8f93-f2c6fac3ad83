# 🛠️ Development Guidelines - HaloBantu Project

## 📋 Panduan Pengembangan dengan Struktur Baru

Dokumen ini berisi panduan untuk developer dalam menggunakan struktur project yang telah direorganisasi.

## 🏗️ Struktur Project Overview

```
src/
├── components/     # Reusable UI components
├── features/       # Feature-based modules  
├── hooks/          # Custom React hooks
├── services/       # API calls & external services
├── utils/          # Utility functions
├── constants/      # App constants
├── context/        # React Context providers
├── assets/         # Static assets
└── styles/         # Global styles
```

## 📁 Panduan Penempatan File

### 1. **Components (`src/components/`)**

#### **UI Components (`src/components/ui/`)**
Untuk komponen UI dasar yang dapat digunakan ulang:
```javascript
// ✅ GOOD
src/components/ui/Button.js
src/components/ui/Card.js
src/components/ui/Modal.js
src/components/ui/Input.js
```

#### **Layout Components (`src/components/layout/`)**
Untuk komponen layout utama:
```javascript
// ✅ GOOD
src/components/layout/Header.js
src/components/layout/Footer.js
src/components/layout/Sidebar.js
src/components/layout/Layout.js
```

#### **Form Components (`src/components/forms/`)**
Untuk komponen form yang kompleks:
```javascript
// ✅ GOOD
src/components/forms/ContactForm.js
src/components/forms/LoginForm.js
src/components/forms/SearchForm.js
```

### 2. **Features (`src/features/`)**

Setiap fitur memiliki struktur sendiri:
```
src/features/[feature-name]/
├── components/     # Komponen khusus fitur ini
├── pages/          # Halaman utama fitur
├── hooks/          # Custom hooks khusus fitur
├── services/       # API calls khusus fitur
├── utils/          # Utility khusus fitur
└── constants/      # Konstanta khusus fitur
```

#### **Contoh: Psychology Feature**
```
src/features/psychology/
├── pages/
│   ├── PsychologyPage.js
│   └── ConsultationDetail.js
├── components/
│   ├── PsychologistCard.js
│   ├── DoctorScheduleList.js
│   └── ScheduleList.js
├── hooks/
│   └── usePsychologyData.js
├── services/
│   └── psychologyAPI.js
└── constants/
    └── psychologyConstants.js
```

### 3. **Global Folders**

#### **Hooks (`src/hooks/`)**
Untuk custom hooks yang digunakan di multiple features:
```javascript
// ✅ GOOD
src/hooks/useAuth.js
src/hooks/useLocalStorage.js
src/hooks/useAPI.js
```

#### **Services (`src/services/`)**
Untuk API calls dan external services:
```javascript
// ✅ GOOD
src/services/api.js
src/services/authService.js
src/services/httpClient.js
```

#### **Utils (`src/utils/`)**
Untuk utility functions:
```javascript
// ✅ GOOD
src/utils/formatters.js
src/utils/validators.js
src/utils/helpers.js
```

#### **Constants (`src/constants/`)**
Untuk konstanta aplikasi:
```javascript
// ✅ GOOD
src/constants/routes.js
src/constants/apiEndpoints.js
src/constants/appConfig.js
```

## 📝 Naming Conventions

### 1. **File Names**
- **Components**: PascalCase (`UserProfile.js`)
- **Hooks**: camelCase dengan prefix 'use' (`useUserData.js`)
- **Services**: camelCase dengan suffix 'Service' (`authService.js`)
- **Utils**: camelCase (`formatDate.js`)
- **Constants**: camelCase (`apiEndpoints.js`)

### 2. **Folder Names**
- **Features**: kebab-case (`daily-services`, `psychology`)
- **Components**: camelCase (`ui`, `layout`, `forms`)

### 3. **Import/Export**
```javascript
// ✅ GOOD - Named exports untuk utilities
export const formatDate = (date) => { ... };
export const formatCurrency = (amount) => { ... };

// ✅ GOOD - Default exports untuk components
const UserProfile = () => { ... };
export default UserProfile;
```

## 🔗 Import Guidelines

### 1. **Import Order**
```javascript
// 1. External libraries
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';

// 2. Internal components (absolute imports)
import Layout from '../../../components/layout/Layout';
import Button from '../../../components/ui/Button';

// 3. Feature-specific components (relative imports)
import PsychologistCard from '../components/PsychologistCard';
import { usePsychologyData } from '../hooks/usePsychologyData';

// 4. Utils, constants, services
import { formatDate } from '../../../utils/formatters';
import { API_ENDPOINTS } from '../../../constants/apiEndpoints';
```

### 2. **Import Paths**
```javascript
// ✅ GOOD - Relative imports within same feature
import PsychologistCard from '../components/PsychologistCard';

// ✅ GOOD - Absolute imports for shared components
import Button from '../../../components/ui/Button';

// ❌ AVOID - Long relative paths
import Button from '../../../../../../../components/ui/Button';
```

## 🆕 Menambah Fitur Baru

### 1. **Buat Struktur Folder**
```bash
mkdir src/features/new-feature
mkdir src/features/new-feature/components
mkdir src/features/new-feature/pages
mkdir src/features/new-feature/hooks
mkdir src/features/new-feature/services
mkdir src/features/new-feature/utils
mkdir src/features/new-feature/constants
```

### 2. **Buat Main Page**
```javascript
// src/features/new-feature/pages/NewFeaturePage.js
import React from 'react';
import Layout from '../../../components/layout/Layout';

const NewFeaturePage = () => {
  return (
    <Layout>
      <div>
        <h1>New Feature</h1>
        {/* Feature content */}
      </div>
    </Layout>
  );
};

export default NewFeaturePage;
```

### 3. **Update App.js**
```javascript
// src/App.js
import NewFeaturePage from './features/new-feature/pages/NewFeaturePage';

// Add route
<Route path="/new-feature" element={<NewFeaturePage />} />
```

## 🧪 Testing Guidelines

### 1. **Test File Location**
```
src/features/psychology/
├── components/
│   ├── PsychologistCard.js
│   └── __tests__/
│       └── PsychologistCard.test.js
├── pages/
│   ├── PsychologyPage.js
│   └── __tests__/
│       └── PsychologyPage.test.js
```

### 2. **Test Naming**
```javascript
// ✅ GOOD
describe('PsychologistCard', () => {
  test('should render psychologist name', () => {
    // test implementation
  });
});
```

## 🚀 Best Practices

### 1. **Component Organization**
- Satu komponen per file
- Gunakan functional components dengan hooks
- Pisahkan logic dan presentation

### 2. **State Management**
- Local state untuk UI state
- Context untuk shared state dalam feature
- Global state management untuk app-wide state

### 3. **Performance**
- Gunakan React.memo untuk komponen yang sering re-render
- Lazy loading untuk pages
- Code splitting per feature

### 4. **Error Handling**
- Error boundaries untuk setiap feature
- Consistent error messaging
- Proper loading states

## 📚 Resources

- [React Best Practices](https://react.dev/learn)
- [Feature-Based Architecture](https://feature-sliced.design/)
- [Clean Code JavaScript](https://github.com/ryanmcdermott/clean-code-javascript)

---
**Happy Coding! 🚀**
