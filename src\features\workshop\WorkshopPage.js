import React, { useState } from 'react';
import { FaSearch, FaMapMarkerAlt, FaDirections, FaPhone, FaStar, FaArrowLeft } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';

const WorkshopPage = () => {
  const navigate = useNavigate();
  const [location, setLocation] = useState('');
  const [workshops, setWorkshops] = useState([]);
  const [message, setMessage] = useState('Masukkan lokasi Anda atau gunakan lokasi saat ini untuk mencari bengkel terdekat.');

  // Dummy data for workshops
  const dummyWorkshops = [
    {
      id: 1,
      name: '<PERSON>gkel Jaya Abadi',
      address: 'Jl. Merdeka No. 10, Jakarta Pusat',
      distance: '2.5 km',
      rating: 4.5,
      phone: '08123456789',
      services: ['Ganti Oli', '<PERSON><PERSON>', 'Tune Up']
    },
    {
      id: 2,
      name: 'Bintang Motor Service',
      address: 'Jl. Sudir<PERSON> No. 25, Jakarta Selatan',
      distance: '3.1 km',
      rating: 4.8,
      phone: '08765432100',
      services: ['Perbaikan Rem', 'Servis Berat', 'Cat Mobil']
    },
    {
      id: 3,
      name: 'Global Auto Care',
      address: 'Jl. Gatot Subroto No. 5, Jakarta Barat',
      distance: '1.8 km',
      rating: 4.2,
      phone: '08234567890',
      services: ['Kaki-kaki', 'Spooring Balancing', 'AC Mobil']
    },
  ];

  const handleSearch = () => {
    if (location.trim() === '') {
      setMessage('Mohon masukkan lokasi atau gunakan lokasi saat ini.');
      setWorkshops([]);
      return;
    }
    // Simulate API call with dummy data
    setMessage(`Mencari bengkel terdekat dari: ${location}...`);
    setTimeout(() => {
      setWorkshops(dummyWorkshops);
      setMessage(`Ditemukan ${dummyWorkshops.length} bengkel terdekat dari ${location}.`);
    }, 1000);
  };

  const handleGetCurrentLocation = () => {
    setMessage('Mendapatkan lokasi saat ini...');
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          setLocation(`Lat: ${latitude}, Long: ${longitude}`);
          // Simulate API call with dummy data after getting location
          setTimeout(() => {
            setWorkshops(dummyWorkshops);
            setMessage(`Ditemukan ${dummyWorkshops.length} bengkel terdekat dari lokasi Anda.`);
          }, 1000);
        },
        (error) => {
          console.error("Error getting location:", error);
          setMessage('Gagal mendapatkan lokasi. Mohon izinkan akses lokasi di browser Anda.');
          setWorkshops([]);
        }
      );
    } else {
      setMessage('Geolokasi tidak didukung oleh browser Anda.');
      setWorkshops([]);
    }
  };

  return (
    <div className="bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-800 text-center">
            Cari Bengkel Terdekat
          </h1>
          <p className="text-gray-600 text-center mt-2">
            Temukan bengkel terpercaya di sekitar Anda
          </p>
        </div>

        {/* Search Input */}
        <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
          <div className="flex items-center border border-gray-300 rounded-lg p-2 mb-4">
            <FaMapMarkerAlt className="text-gray-400 mr-3" />
            <input
              type="text"
              placeholder="Masukkan lokasi Anda (contoh: Jakarta Pusat)"
              className="flex-grow outline-none text-gray-700"
              value={location}
              onChange={(e) => setLocation(e.target.value)}
              onKeyPress={(e) => { if (e.key === 'Enter') handleSearch(); }}
            />
            <button
              onClick={handleSearch}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg ml-3 hover:bg-blue-700 transition-colors"
            >
              <FaSearch />
            </button>
          </div>
          <button
            onClick={handleGetCurrentLocation}
            className="w-full bg-green-500 text-white py-2 rounded-lg hover:bg-green-600 transition-colors flex items-center justify-center"
          >
            <FaMapMarkerAlt className="mr-2" />
            Gunakan Lokasi Saat Ini
          </button>
        </div>

        {/* Message / Results */}
        <p className="text-center text-gray-600 mb-6">{message}</p>

        <div className="space-y-6">
          {workshops.map((workshop) => (
            <div key={workshop.id} className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-800 mb-2">{workshop.name}</h3>
              <p className="text-gray-600 mb-2 flex items-center"><FaMapMarkerAlt className="mr-2 text-blue-500" /> {workshop.address}</p>
              <p className="text-gray-600 mb-4">Jarak: {workshop.distance}</p>
              <div className="flex items-center text-yellow-500 mb-4">
                {[...Array(Math.floor(workshop.rating))].map((_, i) => (
                  <FaStar key={i} className="mr-1" />
                ))}
                <span className="ml-1 text-gray-600">({workshop.rating})</span>
              </div>
              <div className="mb-4">
                <h4 className="font-semibold text-gray-800 mb-2">Layanan:</h4>
                <ul className="flex flex-wrap gap-2">
                  {workshop.services.map((service, index) => (
                    <li key={index} className="bg-gray-200 px-3 py-1 rounded-full text-sm text-gray-700">
                      {service}
                    </li>
                  ))}
                </ul>
              </div>
              <div className="flex space-x-4">
                <a 
                  href={`tel:${workshop.phone}`}
                  className="flex-1 bg-blue-600 text-white py-2 rounded-lg text-center font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center"
                >
                  <FaPhone className="mr-2" /> Hubungi
                </a>
                <button
                  onClick={() => alert(`Membuka rute ke ${workshop.name}`)}
                  className="flex-1 bg-gray-200 text-gray-800 py-2 rounded-lg text-center font-semibold hover:bg-gray-300 transition-colors flex items-center justify-center"
                >
                  <FaDirections className="mr-2" /> Rute
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default WorkshopPage; 