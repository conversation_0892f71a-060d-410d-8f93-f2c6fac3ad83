import React, { useState } from 'react';
import { FaMapMarkerAlt, FaDirections, FaPhone, FaStar, FaFilter } from 'react-icons/fa';

const WorkshopPage = () => {
  const [location, setLocation] = useState('');
  const [workshops, setWorkshops] = useState([]);
  const [message, setMessage] = useState('Masukkan lokasi Anda atau gunakan lokasi saat ini untuk mencari bengkel terdekat.');
  const [selectedCategory, setSelectedCategory] = useState('Semua');
  const [showProducts, setShowProducts] = useState(false);

  // Dummy data for workshops
  const dummyWorkshops = [
    {
      id: 1,
      name: 'Ronggolame Motor',
      address: 'Jl. Raya Bogor No. 15',
      distance: '2.5 km',
      rating: 4.5,
      phone: '08123456789',
      services: ['<PERSON>anti <PERSON>li', '<PERSON><PERSON>', 'Tune Up'],
      description: 'Spesialis Motor Honda'
    },
    {
      id: 2,
      name: 'Jaya Motor',
      address: 'Jl. Sudirman No. 25',
      distance: '3.1 km',
      rating: 4.8,
      phone: '08765432100',
      services: ['Perbaikan Rem', 'Servis Berat', 'Cat Motor'],
      description: 'Bengkel Motor Terpercaya'
    },
  ];

  // Product categories and data
  const categories = ['Semua', 'Ban', 'Oli', 'Sparepart'];

  const products = [
    {
      id: 1,
      name: 'Ban Tubeless',
      category: 'Ban',
      price: 'Rp 250.000',
      image: 'https://images.pexels.com/photos/190819/pexels-photo-190819.jpeg?auto=compress&cs=tinysrgb&w=300',
      description: 'Ban motor tubeless berkualitas tinggi'
    },
    {
      id: 2,
      name: 'Oli Mesin',
      category: 'Oli',
      price: 'Rp 45.000',
      image: 'https://images.pexels.com/photos/279949/pexels-photo-279949.jpeg?auto=compress&cs=tinysrgb&w=300',
      description: 'Oli mesin SAE 10W-40'
    },
    {
      id: 3,
      name: 'Ban Motor',
      category: 'Ban',
      price: 'Rp 180.000',
      image: 'https://images.pexels.com/photos/190819/pexels-photo-190819.jpeg?auto=compress&cs=tinysrgb&w=300',
      description: 'Ban motor standar'
    },
    {
      id: 4,
      name: 'Oli Motor',
      category: 'Oli',
      price: 'Rp 35.000',
      image: 'https://images.pexels.com/photos/279949/pexels-photo-279949.jpeg?auto=compress&cs=tinysrgb&w=300',
      description: 'Oli motor 4T'
    },
  ];

  const filteredProducts = selectedCategory === 'Semua'
    ? products
    : products.filter(product => product.category === selectedCategory);

  const handleSearch = () => {
    if (location.trim() === '') {
      setMessage('Mohon masukkan lokasi atau gunakan lokasi saat ini.');
      setWorkshops([]);
      return;
    }
    // Simulate API call with dummy data
    setMessage(`Mencari bengkel terdekat dari: ${location}...`);
    setTimeout(() => {
      setWorkshops(dummyWorkshops);
      setMessage(`Ditemukan ${dummyWorkshops.length} bengkel terdekat dari ${location}.`);
      setShowProducts(true);
    }, 1000);
  };

  const handleGetCurrentLocation = () => {
    setMessage('Mendapatkan lokasi saat ini...');
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          setLocation(`Lat: ${latitude}, Long: ${longitude}`);
          // Simulate API call with dummy data after getting location
          setTimeout(() => {
            setWorkshops(dummyWorkshops);
            setMessage(`Ditemukan ${dummyWorkshops.length} bengkel terdekat dari lokasi Anda.`);
            setShowProducts(true);
          }, 1000);
        },
        (error) => {
          console.error("Error getting location:", error);
          setMessage('Gagal mendapatkan lokasi. Mohon izinkan akses lokasi di browser Anda.');
          setWorkshops([]);
        }
      );
    } else {
      setMessage('Geolokasi tidak didukung oleh browser Anda.');
      setWorkshops([]);
    }
  };

  return (
    <div className="bg-gray-50 min-h-screen py-8">
      <div className="max-w-6xl mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-800 text-center">
            Temukan Bengkel Terdekat
          </h1>
          <p className="text-gray-600 text-center mt-2">
            Cari dan temukan bengkel terpercaya dengan berbagai produk berkualitas
          </p>
        </div>

        {/* Search Section */}
        <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Temukan Bengkel Terdekat</h2>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <input
                type="text"
                placeholder="Cari lokasi"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg outline-none focus:border-blue-500 transition-colors"
                value={location}
                onChange={(e) => setLocation(e.target.value)}
                onKeyPress={(e) => { if (e.key === 'Enter') handleSearch(); }}
              />
            </div>
            <button
              onClick={handleSearch}
              className="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors font-semibold"
            >
              Temukan
            </button>
            <button
              onClick={handleGetCurrentLocation}
              className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors font-semibold flex items-center"
            >
              <FaMapMarkerAlt className="mr-2" />
              Lokasi Saya
            </button>
          </div>
          <p className="text-sm text-gray-500 mt-2">
            Cari dan temukan bengkel terpercaya dengan berbagai produk berkualitas
          </p>
        </div>

        {/* Message */}
        {message && (
          <div className="text-center text-gray-600 mb-6 bg-blue-50 p-4 rounded-lg">
            {message}
          </div>
        )}

        {/* Workshop Results */}
        {workshops.length > 0 && (
          <div className="mb-12">
            <h2 className="text-2xl font-bold text-gray-800 mb-6">Bengkel Terdekat</h2>
            <div className="grid md:grid-cols-2 gap-6">
              {workshops.map((workshop) => (
                <div key={workshop.id} className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-xl font-bold text-gray-800 mb-1">{workshop.name}</h3>
                      <p className="text-sm text-gray-500">{workshop.description}</p>
                    </div>
                    <div className="flex items-center text-yellow-500">
                      <FaStar className="mr-1" />
                      <span className="text-gray-600 text-sm">{workshop.rating}</span>
                    </div>
                  </div>

                  <div className="space-y-2 mb-4">
                    <p className="text-gray-600 flex items-center text-sm">
                      <FaMapMarkerAlt className="mr-2 text-blue-500" />
                      {workshop.address}
                    </p>
                    <p className="text-gray-600 text-sm">Jarak: {workshop.distance}</p>
                  </div>

                  <div className="mb-4">
                    <p className="text-sm font-medium text-gray-700 mb-2">Layanan:</p>
                    <div className="flex flex-wrap gap-1">
                      {workshop.services.map((service, index) => (
                        <span key={index} className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                          {service}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div className="flex space-x-3">
                    <a
                      href={`tel:${workshop.phone}`}
                      className="flex-1 bg-blue-600 text-white py-2 rounded-lg text-center text-sm font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center"
                    >
                      <FaPhone className="mr-2" /> Hubungi
                    </a>
                    <button
                      onClick={() => alert(`Membuka rute ke ${workshop.name}`)}
                      className="flex-1 bg-gray-200 text-gray-800 py-2 rounded-lg text-center text-sm font-semibold hover:bg-gray-300 transition-colors flex items-center justify-center"
                    >
                      <FaDirections className="mr-2" /> Rute
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        {/* Product Section */}
        {showProducts && (
          <div className="bg-white rounded-xl shadow-lg p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-gray-800">Pilih Produk</h2>
              <button className="bg-black text-white px-4 py-2 rounded-lg text-sm flex items-center">
                <FaFilter className="mr-2" />
                Lihat Semua/Filter
              </button>
            </div>

            {/* Category Filter */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-3">Kategori</h3>
              <div className="flex flex-wrap gap-2">
                {categories.map((category) => (
                  <button
                    key={category}
                    onClick={() => setSelectedCategory(category)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                      selectedCategory === category
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                    }`}
                  >
                    {category}
                  </button>
                ))}
              </div>
            </div>

            {/* Product Grid */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {filteredProducts.map((product) => (
                <div key={product.id} className="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div className="aspect-square mb-3 bg-white rounded-lg overflow-hidden">
                    <img
                      src={product.image}
                      alt={product.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <h4 className="font-semibold text-gray-800 text-sm mb-1">{product.name}</h4>
                  <p className="text-xs text-gray-600 mb-2">{product.description}</p>
                  <p className="font-bold text-blue-600 text-sm">{product.price}</p>
                </div>
              ))}
            </div>

            {filteredProducts.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                Tidak ada produk dalam kategori ini
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default WorkshopPage;