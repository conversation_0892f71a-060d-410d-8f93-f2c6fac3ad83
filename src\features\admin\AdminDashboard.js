import React from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';

const AdminDashboard = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const navLinks = [
    { path: '/admin/psychologists', label: '<PERSON><PERSON><PERSON>olog' },
    { path: '/admin/workshops', label: '<PERSON><PERSON><PERSON>' },
    { path: '/admin/daily-services', label: '<PERSON><PERSON><PERSON>' },
    { path: '/admin/users', label: 'Ke<PERSON>la Pengguna' },
    { path: '/admin/transactions', label: 'Kelola Transaksi' },
  ];

  const handleLogout = () => {
    // TODO: Implement actual logout logic (clear token, etc.)
    navigate('/login'); // Redirect to login page
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex">
      {/* Sidebar */}
      <div className="w-64 bg-gray-800 text-white flex flex-col shadow-lg">
        <div className="p-6 text-3xl font-extrabold text-blue-300 border-b border-gray-700">
          HaloBantu Admin
        </div>
        <nav className="flex-1 px-4 py-8 space-y-3">
          {navLinks.map((link) => (
            <Link
              key={link.path}
              to={link.path}
              className={`block px-5 py-3 rounded-lg text-lg font-medium transition-all duration-300 
                ${location.pathname === link.path
                  ? 'bg-blue-600 text-white shadow-md'
                  : 'hover:bg-gray-700 hover:text-blue-200'
                }`}
            >
              {link.label}
            </Link>
          ))}
        </nav>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        <header className="bg-white shadow-md p-6 border-b border-gray-200 flex justify-between items-center">
          <h1 className="text-3xl font-bold text-gray-800">Admin Dashboard</h1>
          <div className="flex items-center space-x-4">
            <span className="text-gray-600 text-lg">Selamat Datang, Admin!</span>
            <button
              onClick={handleLogout}
              className="px-4 py-2 text-sm text-red-600 bg-red-100 rounded-md hover:bg-red-200 transition-colors font-semibold"
            >
              Logout
            </button>
          </div>
        </header>
        <main className="flex-1 p-10">
          <div className="bg-white rounded-xl shadow-xl p-8 border border-gray-100">
            <h2 className="text-3xl font-extrabold text-gray-800 mb-4">Selamat Datang di Admin Panel</h2>
            <p className="text-gray-600 text-lg leading-relaxed">
              Gunakan menu navigasi di samping untuk mengelola berbagai aspek aplikasi HaloBantu. 
              Anda dapat menambah, mengedit, atau menghapus data psikolog, bengkel, jasa harian, pengguna, dan memantau transaksi.
            </p>
          </div>
        </main>
      </div>
    </div>
  );
};

export default AdminDashboard; 