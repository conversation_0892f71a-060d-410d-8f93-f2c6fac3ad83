import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import PsychologistCard from '../components/PsychologistCard';
import DoctorScheduleList from '../components/DoctorScheduleList';
import BackButton from '../../../components/ui/BackButton';

const PsychologyPage = () => {
  const navigate = useNavigate();
  const [selectedPsychologist, setSelectedPsychologist] = useState(null);

  const psychologists = [
    {
      id: 1,
      name: 'Dr. <PERSON>',
      specialization: 'Psikolog Klinis',
      rating: 4.8,
      image: 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      bio: 'Spesialis dalam menangani masalah kecemasan dan depresi dengan pengalaman lebih dari 10 tahun.',
      availableHours: 20,
      experience: [
        'Psikolog Klinis di RS Medika (2018-se<PERSON><PERSON>)',
        'Konsultan di Pusat Ke<PERSON>hatan Mental (2015-2018)',
        'Peneliti di Institut Psikologi Nasional (2012-2015)'
      ],
      education: [
        'Ph.D. in Clinical Psychology, Universitas Indonesia',
        'M.Psi., Psikologi Klinis, Universitas Gadjah Mada',
        'S.Psi., Psikologi, Universitas Indonesia'
      ]
    },
    {
      id: 2,
      name: 'Dr. Michael Chen',
      specialization: 'Psikolog Anak & Remaja',
      rating: 4.9,
      image: 'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      bio: 'Ahli dalam perkembangan anak dan remaja, dengan fokus pada masalah perilaku dan pembelajaran.',
      availableHours: 15,
      experience: [
        'Psikolog Anak di Klinik Anak Sejahtera (2019-sekarang)',
        'Konsultan Sekolah di Sekolah Internasional (2016-2019)',
        'Terapis Anak di Pusat Tumbuh Kembang (2014-2016)'
      ],
      education: [
        'Ph.D. in Child Psychology, Universitas Airlangga',
        'M.Psi., Psikologi Anak, Universitas Padjadjaran',
        'S.Psi., Psikologi, Universitas Indonesia'
      ]
    },
    {
      id: 3,
      name: 'Dr. Maya Putri',
      specialization: 'Psikolog Keluarga',
      rating: 4.7,
      image: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      bio: 'Spesialis dalam konseling keluarga dan hubungan, membantu keluarga membangun komunikasi yang sehat.',
      availableHours: 18,
      experience: [
        'Psikolog Keluarga di Pusat Konseling Keluarga (2017-sekarang)',
        'Konselor di Lembaga Bantuan Hukum (2015-2017)',
        'Terapis Keluarga di Klinik Psikologi (2013-2015)'
      ],
      education: [
        'Ph.D. in Family Psychology, Universitas Gadjah Mada',
        'M.Psi., Psikologi Keluarga, Universitas Indonesia',
        'S.Psi., Psikologi, Universitas Padjadjaran'
      ]
    }
  ];

  const handleScheduleSelect = (psychologistWithSchedule) => {
    setSelectedPsychologist(psychologistWithSchedule);
    navigate('/psychology/consultation', { state: { psychologist: psychologistWithSchedule } });
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8 relative">
      <div className="max-w-7xl mx-auto px-4 pt-16">
        <BackButton />
        
        <h1 className="text-3xl font-bold text-gray-800 text-center mb-8">
          Konsultasi Psikologi
        </h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {psychologists.map((psychologist) => (
            <PsychologistCard
              key={psychologist.id}
              psychologist={psychologist}
              onSelect={handleScheduleSelect}
            />
          ))}
        </div>

        <DoctorScheduleList psychologists={psychologists} />
      </div>
    </div>
  );
};

export default PsychologyPage; 