import React from 'react';
import { FaTools, FaUserMd, FaHandsHelping } from 'react-icons/fa';

const ServicesSection = () => {
  const services = [
    {
      title: "Bengkel",
      description: "Layanan perbaikan dan perawatan kendaraan dengan teknologi canggih dan teknisi berpengalaman untuk performa optimal kendaraan masa depan.",
      icon: <FaTools className="text-4xl" />,
      color: "from-blue-500 to-blue-600",
      path: "/workshop",
      image: "https://images.pexels.com/photos/3806288/pexels-photo-3806288.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop"
    },
    {
      title: "Konsultasi Psikologi",
      description: "Konsultasi profesional dengan psikolog berpengalaman untuk kesehatan mental dan pengembangan diri yang optimal di era digital.",
      icon: <FaUserMd className="text-4xl" />,
      color: "from-purple-500 to-purple-600",
      path: "/psychology",
      image: "https://images.pexels.com/photos/7176026/pexels-photo-7176026.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop"
    },
    {
      title: "Opo Wae",
      description: "Solusi lengkap untuk berbagai kebutuhan sehari-hari dengan layanan berkualitas tinggi dan teknologi terdepan untuk kemudahan hidup.",
      icon: <FaHandsHelping className="text-4xl" />,
      color: "from-green-500 to-green-600",
      path: "/daily-services",
      image: "https://images.pexels.com/photos/4792509/pexels-photo-4792509.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop"
    }
  ];

  return (
    <section id="services" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
            Layanan Kami
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Solusi teknologi terdepan untuk masa depan yang lebih baik dengan layanan berkualitas premium
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {services.map((service, index) => (
            <div
              key={index}
              className="group bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2"
            >
              {/* Service Image */}
              <div className="relative h-48 overflow-hidden">
                <img
                  src={service.image}
                  alt={service.title}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                />
                <div className={`absolute inset-0 bg-gradient-to-t ${service.color} opacity-80`}></div>
                <div className="absolute inset-0 flex items-center justify-center text-white">
                  {service.icon}
                </div>
              </div>

              {/* Service Content */}
              <div className="p-6">
                <h3 className="text-2xl font-bold text-gray-800 mb-3">
                  {service.title}
                </h3>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  {service.description}
                </p>
                <button
                  onClick={() => window.location.href = service.path}
                  className={`w-full py-3 px-6 bg-gradient-to-r ${service.color} text-white font-semibold rounded-lg hover:shadow-lg transition-all duration-300 transform hover:scale-105`}
                >
                  Lihat Layanan
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
