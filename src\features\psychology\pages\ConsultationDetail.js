import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { FaArrowLeft, FaUser, FaCalendarAlt, FaClock, FaMoneyBillWave } from 'react-icons/fa';

const ConsultationDetail = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { psychologist } = location.state || {};

  if (!psychologist) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4">
          <div className="mb-8">
            <button
              onClick={() => navigate('/dashboard')}
              className="flex items-center text-gray-600 hover:text-gray-800 mb-4"
            >
              <FaArrowLeft className="mr-2" />
              Kembali ke Dashboard
            </button>
          </div>
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h2 className="text-2xl font-bold text-gray-800 mb-4">
              Data Tidak Ditemukan
            </h2>
            <p className="text-gray-600 mb-4">
              Maaf, data konsultasi tidak ditemukan. Silakan kembali ke halaman sebelumnya.
            </p>
            <button
              onClick={() => navigate('/psychology')}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
            >
              Kembali ke Halaman Psikolog
            </button>
          </div>
        </div>
      </div>
    );
  }

  const handleBack = () => {
    navigate(-1);
  };

  const handleConfirm = () => {
    // TODO: Implementasi logika konfirmasi dan pembayaran
    console.log('Konfirmasi konsultasi:', psychologist);
  };

  return (
    <div className="bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={handleBack}
            className="flex items-center text-gray-600 hover:text-gray-800 mb-4"
          >
            <FaArrowLeft className="mr-2" />
            Kembali ke Daftar Psikolog
          </button>
          <h1 className="text-2xl font-bold text-gray-800">Detail Konsultasi</h1>
        </div>

        {/* Card Detail Konsultasi */}
        <div className="bg-white rounded-xl shadow-lg overflow-hidden">
          <div className="p-6">

            {/* Info Psikolog */}
            <div className="flex items-start space-x-4 mb-8">
              <img
                src={psychologist.image}
                alt={psychologist.name}
                className="w-24 h-24 rounded-full object-cover"
              />
              <div>
                <h3 className="text-xl font-semibold text-gray-800">
                  {psychologist.name}
                </h3>
                <p className="text-gray-600">{psychologist.specialization}</p>
              </div>
            </div>

            {/* Jadwal Terpilih */}
            <div className="bg-blue-50 rounded-lg p-4 mb-8">
              <h4 className="font-semibold text-gray-800 mb-4">Jadwal Terpilih</h4>
              <div className="flex items-center space-x-4">
                <div className="flex items-center text-gray-600">
                  <FaCalendarAlt className="mr-2" />
                  <span>{psychologist.selectedSchedule.day}</span>
                </div>
                <div className="flex items-center text-gray-600">
                  <FaClock className="mr-2" />
                  <span>{psychologist.selectedSchedule.time}</span>
                </div>
              </div>
            </div>

            {/* Ringkasan Biaya */}
            <div className="border-t border-gray-200 pt-6">
              <h4 className="font-semibold text-gray-800 mb-4">Ringkasan Biaya</h4>
              <div className="space-y-2">
                <div className="flex justify-between text-gray-600">
                  <span>Biaya Konsultasi</span>
                  <span>Rp 300.000</span>
                </div>
                <div className="flex justify-between text-gray-600">
                  <span>Biaya Platform</span>
                  <span>Rp 20.000</span>
                </div>
                <div className="border-t border-gray-200 pt-2 mt-2">
                  <div className="flex justify-between font-semibold text-gray-800">
                    <span>Total</span>
                    <span>Rp 320.000</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Tombol Konfirmasi */}
            <div className="mt-8">
              <button
                onClick={handleConfirm}
                className="w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
              >
                Konfirmasi & Bayar
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConsultationDetail; 