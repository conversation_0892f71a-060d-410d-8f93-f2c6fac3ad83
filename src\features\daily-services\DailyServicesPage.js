import React, { useState } from 'react';
import { FaSearch, FaClock, FaStar, FaPhone, FaArrowLeft } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';

const DailyServicesPage = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState('Semua');
  const [services, setServices] = useState([]);
  const [message, setMessage] = useState('Cari layanan yang Anda butuhkan.');

  // Dummy data for daily services
  const dummyServices = [
    {
      id: 1,
      name: '<PERSON><PERSON>',
      serviceType: 'Sopir Pribadi',
      availability: 'Tersedia Sekarang',
      rating: 4.9,
      phone: '081122334455',
      description: 'Sopir berpengalaman dengan rekor bersih dan pelayanan ramah, siap mengantar Anda dengan aman dan nyaman.'
    },
    {
      id: 2,
      name: '<PERSON><PERSON>',
      serviceType: '<PERSON><PERSON><PERSON><PERSON> Rumah',
      availability: '<PERSON><PERSON>',
      rating: 4.7,
      phone: '085566778899',
      description: 'Profesional dalam kebersihan dan kerapian rumah, membuat hunian Anda bersinar.'
    },
    {
      id: 3,
      name: 'Joko Susilo',
      serviceType: 'Tukang Pijat Panggilan',
      availability: 'Besok Pagi',
      rating: 4.6,
      phone: '081234567890',
      description: 'Pijat relaksasi dan terapi untuk kesehatan tubuh, mengurangi stres dan nyeri otot.'
    },
    {
      id: 4,
      name: 'Rina Dewi',
      serviceType: 'Babysitter',
      availability: 'Tersedia Sekarang',
      rating: 5.0,
      phone: '087890123456',
      description: 'Pengasuh anak yang sabar dan berpengalaman, memastikan buah hati Anda aman dan ceria.'
    }
  ];

  const categories = [
    'Semua', 'Sopir Pribadi', 'Pembersih Rumah', 'Tukang Pijat Panggilan', 'Babysitter'
  ];

  const handleSearch = () => {
    setMessage('Mencari layanan...');
    setTimeout(() => {
      const filteredServices = dummyServices.filter(service => 
        (filter === 'Semua' || service.serviceType.toLowerCase().includes(filter.toLowerCase())) &&
        service.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setServices(filteredServices);
      setMessage(`Ditemukan ${filteredServices.length} layanan yang sesuai.`);
    }, 500);
  };

  const handleCategoryClick = (category) => {
    setFilter(category);
    setSearchTerm(''); // Clear search term when category is selected
    setMessage(`Menampilkan kategori: ${category}`);
    setTimeout(() => {
      const filteredServices = dummyServices.filter(service => 
        (category === 'Semua' || service.serviceType.toLowerCase().includes(category.toLowerCase()))
      );
      setServices(filteredServices);
      setMessage(`Ditemukan ${filteredServices.length} layanan dalam kategori ${category}.`);
    }, 500);
  };

  return (
    <div className="bg-gradient-to-br from-blue-50 to-indigo-100 py-10">
      <div className="max-w-5xl mx-auto px-6">
        {/* Header */}
        <div className="mb-8 text-center">
          <h1 className="text-4xl font-extrabold text-gray-800 mb-2">
            Temukan Kebutuhan Harian Anda
          </h1>
          <p className="text-gray-600 text-lg">
            Berbagai layanan untuk memudahkan aktivitas sehari-hari Anda
          </p>
        </div>

        {/* Search & Filter */}
        <div className="bg-white rounded-2xl shadow-xl p-8 mb-10 border border-gray-100">
          <div className="flex items-center border border-gray-300 rounded-full p-3 mb-6 focus-within:border-blue-500 transition-all duration-300">
            <FaSearch className="text-gray-400 mr-4 text-xl" />
            <input
              type="text"
              placeholder="Cari layanan atau penyedia jasa..."
              className="flex-grow outline-none text-gray-800 text-lg bg-transparent"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyPress={(e) => { if (e.key === 'Enter') handleSearch(); }}
            />
            <button
              onClick={handleSearch}
              className="bg-blue-600 text-white px-6 py-2 rounded-full ml-4 hover:bg-blue-700 transition-colors duration-300 flex items-center justify-center"
            >
              Cari
            </button>
          </div>

          <div className="flex flex-wrap gap-3 justify-center">
            {categories.map((cat) => (
              <button
                key={cat}
                onClick={() => handleCategoryClick(cat)}
                className={`px-5 py-2 rounded-full text-base font-medium transition-all duration-300 ${
                  filter === cat
                    ? 'bg-blue-600 text-white shadow-md'
                    : 'bg-gray-100 text-gray-700 hover:bg-blue-100 hover:text-blue-700'
                }`}
              >
                {cat}
              </button>
            ))}
          </div>
        </div>

        {/* Message / Results */}
        <p className="text-center text-gray-600 text-lg mb-8">{message}</p>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.length > 0 ? (
            services.map((service) => (
              <div key={service.id} className="bg-white rounded-2xl shadow-lg p-7 border border-gray-100 transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
                <h3 className="text-2xl font-bold text-gray-800 mb-3">{service.name}</h3>
                <p className="text-blue-600 text-lg font-semibold mb-2">{service.serviceType}</p>
                <p className="text-green-600 text-md font-medium mb-3 flex items-center">
                  <FaClock className="mr-2 text-green-500" /> {service.availability}
                </p>
                <div className="flex items-center text-yellow-500 mb-4">
                  {[...Array(Math.floor(service.rating))].map((_, i) => (
                    <FaStar key={i} className="mr-1" />
                  ))}
                  {service.rating % 1 !== 0 && <FaStar className="mr-1 opacity-50" />}
                  <span className="ml-2 text-gray-600 text-sm">({service.rating} / 5)</span>
                </div>
                <p className="text-gray-700 leading-relaxed mb-6">{service.description}</p>
                <a 
                  href={`tel:${service.phone}`}
                  className="w-full bg-blue-600 text-white py-3 rounded-xl text-center font-bold text-lg hover:bg-blue-700 transition-colors duration-300 flex items-center justify-center"
                >
                  <FaPhone className="mr-3" /> Hubungi
                </a>
              </div>
            ))
          ) : (
            <p className="text-center text-gray-500 text-lg col-span-full">Belum ada layanan yang ditemukan. Coba cari atau pilih kategori di atas.</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default DailyServicesPage; 